/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #8b5cf6;
    --primary-dark: #7c3aed;
    --secondary-color: #06d6a0;
    --accent-color: #f72585;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-light: #94a3b8;
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-card: #334155;
    --bg-dark: #020617;
    --border-color: #475569;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
    --gradient-primary: linear-gradient(135deg, #8b5cf6, #7c3aed, #6d28d9);
    --gradient-secondary: linear-gradient(135deg, #06d6a0, #059669, #047857);
    --gradient-hero: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #7c3aed 50%, #8b5cf6 75%, #a855f7 100%);
    --gradient-accent: linear-gradient(135deg, #f72585, #e91e63, #d81b60);
    --gradient-card: linear-gradient(135deg, #334155, #475569, #64748b);
    --gradient-dark: linear-gradient(135deg, #020617, #0f172a, #1e293b);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.875rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(15, 23, 42, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    margin-right: 0.5rem;
    font-size: 1.75rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content {
    color: white;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.highlight {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.hero-features {
    display: flex;
    gap: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.feature i {
    color: var(--accent-color);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.tv-mockup {
    width: 400px;
    height: 250px;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 20px;
    position: relative;
    box-shadow: var(--shadow-xl);
    border: 8px solid #333;
}

.tv-screen {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tv-screen i {
    font-size: 3rem;
    color: white;
    opacity: 0.8;
}

/* Stats Section */
.stats {
    padding: 4rem 0;
    background: var(--bg-secondary);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: var(--bg-card);
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease;
    border: 1px solid var(--border-color);
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-light);
}

/* Packages Section */
.packages {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.billing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.toggle-label {
    font-weight: 500;
    color: var(--text-primary);
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.save-badge {
    background: var(--accent-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.package-card {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: var(--shadow-sm);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    text-align: center;
}

.package-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.package-card.popular {
    border-color: var(--primary-color);
    transform: scale(1.05);
    background: var(--gradient-card);
    border: 2px solid var(--primary-color);
    position: relative;
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.3);
}

.package-card.popular::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-primary);
    border-radius: 16px;
    z-index: -1;
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.package-header h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.package-header p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.package-price {
    margin-bottom: 2rem;
}

.price {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
}

.period {
    color: var(--text-secondary);
    font-size: 1rem;
}

.package-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.package-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
}

.package-features i {
    color: var(--accent-color);
    font-size: 0.875rem;
}

/* Channels Section */
.channels {
    padding: 6rem 0;
}

.category-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 12px 24px;
    border: 2px solid var(--border-color);
    background: white;
    color: var(--text-secondary);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.channels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1.5rem;
    max-height: 600px;
    overflow-y: auto;
}

.channel-item {
    background: var(--bg-card);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.channel-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.channel-logo {
    width: 60px;
    height: 60px;
    background: var(--bg-secondary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.channel-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* Testimonials Section */
.testimonials {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
}

.stars i {
    color: #fbbf24;
    font-size: 1.25rem;
}

.testimonial-card p {
    font-style: italic;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.author-info h4 {
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.875rem;
}

/* About Section */
.about {
    padding: 6rem 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.about-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.about-feature i {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.about-feature h4 {
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.about-feature p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

.about-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.about-img-container {
    position: relative;
    width: 300px;
    height: 300px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-card {
    position: absolute;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    animation: float 3s ease-in-out infinite;
}

.floating-card:first-child {
    top: 20%;
    right: -10%;
    animation-delay: 0s;
}

.floating-card:last-child {
    bottom: 20%;
    left: -10%;
    animation-delay: 1.5s;
}

.floating-card i {
    color: var(--primary-color);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* FAQ Section */
.faq {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--bg-card);
    border-radius: 12px;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background: var(--bg-secondary);
}

.faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.faq-question i {
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 6rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.contact-details h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.contact-details p {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.contact-form {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--gradient-dark);
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1.5rem;
    color: white;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer-logo i {
    margin-right: 0.5rem;
    font-size: 1.75rem;
}

.footer-section p {
    color: #9ca3af;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section ul li a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 2rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom p {
    color: #9ca3af;
    margin: 0;
}

.payment-methods {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.payment-methods i {
    font-size: 2rem;
    color: #9ca3af;
    transition: color 0.3s ease;
}

.payment-methods i:hover {
    color: var(--primary-color);
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-card);
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease;
    border: 1px solid var(--border-color);
}

.modal-content.payment-modal {
    max-width: 600px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.close {
    font-size: 2rem;
    cursor: pointer;
    color: var(--text-light);
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    text-align: center;
    margin-top: 1.5rem;
}

.modal-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.modal-footer a:hover {
    text-decoration: underline;
}

/* Form Styles */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
}

.checkbox input {
    margin-right: 0.5rem;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password:hover {
    text-decoration: underline;
}

.trial-info {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.trial-feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.trial-feature:last-child {
    margin-bottom: 0;
}

.trial-feature i {
    color: var(--accent-color);
}

/* Payment Modal Styles */
.payment-summary {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.payment-summary h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.summary-total {
    display: flex;
    justify-content: space-between;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
    border-top: 1px solid var(--border-color);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.payment-methods {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.payment-method {
    flex: 1;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.payment-method.active {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
}

.payment-method i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.payment-form {
    display: none;
}

.payment-form.active {
    display: block;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.billing-info {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.billing-info h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.payment-security {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.payment-security i {
    color: var(--accent-color);
}

.payment-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.paypal-info {
    text-align: center;
    padding: 2rem;
}

.paypal-logo i {
    font-size: 4rem;
    color: #0070ba;
    margin-top: 1rem;
}

.crypto-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.crypto-option {
    padding: 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.crypto-option:hover {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
}

.crypto-option i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Live Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    background: var(--bg-card);
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
    z-index: 1500;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.chat-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    box-shadow: var(--shadow-md);
}

.chat-header i {
    font-size: 1.25rem;
}

.chat-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--accent-color);
    position: absolute;
    right: 1rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.chat-body {
    height: 400px;
    display: none;
    flex-direction: column;
}

.chat-widget.open .chat-body {
    display: flex;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: var(--bg-secondary);
}

.chat-message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.chat-message.bot .message-content {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-radius: 12px 12px 12px 4px;
    border: 1px solid var(--border-color);
}

.chat-message.user .message-content {
    background: var(--primary-color);
    color: white;
    border-radius: 12px 12px 4px 12px;
    margin-left: auto;
}

.message-content {
    padding: 0.75rem 1rem;
    max-width: 80%;
}

.message-content p {
    margin: 0;
    font-size: 0.9rem;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.5rem;
}

.chat-input input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    outline: none;
}

.chat-input button {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.chat-input button:hover {
    background: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-card);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-md);
        padding: 2rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-features {
        flex-direction: column;
        gap: 1rem;
    }

    .tv-mockup {
        width: 300px;
        height: 200px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .packages-grid {
        grid-template-columns: 1fr;
    }

    .package-card.popular {
        transform: none;
    }

    .category-tabs {
        flex-direction: column;
        align-items: center;
    }

    .channels-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-header,
    .modal-body {
        padding: 1.5rem;
    }

    .payment-methods {
        flex-direction: column;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .crypto-options {
        grid-template-columns: 1fr;
    }

    .chat-widget {
        width: 300px;
        bottom: 15px;
        right: 15px;
    }

    .chat-body {
        height: 350px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .feature-card,
    .package-card,
    .testimonial-card {
        padding: 1.5rem;
    }

    .about-img-container {
        width: 250px;
        height: 250px;
    }

    .floating-card {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .chat-widget {
        width: calc(100vw - 30px);
        left: 15px;
        right: 15px;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Messages */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d1fae5;
    border-color: #10b981;
    color: #065f46;
}

.alert-error {
    background-color: #fee2e2;
    border-color: #ef4444;
    color: #991b1b;
}

.alert-info {
    background-color: #dbeafe;
    border-color: #3b82f6;
    color: #1e40af;
}

/* Reseller Page Styles */
.reseller-hero {
    min-height: 80vh;
    display: flex;
    align-items: center;
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
    padding: 8rem 0 4rem;
}

.reseller-hero-content {
    text-align: center;
    color: white;
}

.reseller-hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.reseller-hero-description {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.reseller-hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-bottom: 3rem;
}

.hero-stat {
    text-align: center;
}

.hero-stat .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.hero-stat .stat-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.reseller-hero-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Reseller Benefits */
.reseller-benefits {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    text-align: center;
}

.benefit-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.benefit-icon i {
    font-size: 2rem;
    color: white;
}

.benefit-card h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.benefit-card p {
    color: var(--text-light);
}

/* Reseller Packages */
.reseller-packages {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.reseller-packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.reseller-package-card {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: var(--shadow-sm);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
}

.reseller-package-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.reseller-package-card.popular {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.3);
}

.package-details {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-item.profit {
    border-top: 1px solid var(--border-color);
    padding-top: 0.75rem;
    margin-top: 0.75rem;
    font-weight: 600;
}

.detail-item .label {
    color: var(--text-light);
}

.detail-item .value {
    color: var(--text-primary);
    font-weight: 500;
}

.detail-item.profit .value {
    color: var(--secondary-color);
}

/* Reseller Tools */
.reseller-tools {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.tool-card {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    text-align: center;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.tool-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.tool-icon i {
    font-size: 1.5rem;
    color: white;
}

/* How It Works */
.how-it-works {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step-item {
    text-align: center;
    padding: 2rem;
}

.step-number {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    font-weight: 800;
    color: white;
}

.step-content h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.step-content p {
    color: var(--text-light);
}

/* Success Stories */
.success-stories {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.story-card {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.story-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.story-content {
    margin-bottom: 2rem;
}

.quote-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.story-content p {
    font-style: italic;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.story-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.story-stats {
    display: flex;
    gap: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.story-stats .stat {
    text-align: center;
}

.story-stats .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 0.25rem;
}

.story-stats .stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
}

/* Reseller FAQ */
.reseller-faq {
    padding: 6rem 0;
    background: var(--bg-primary);
}

/* Reseller CTA */
.reseller-cta {
    padding: 6rem 0;
    background: var(--gradient-hero);
    text-align: center;
}

.cta-content {
    color: white;
}

.cta-content h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* Active nav link */
.nav-link.active {
    color: var(--primary-color);
}

/* Responsive Design for Reseller Page */
@media (max-width: 768px) {
    .reseller-hero-title {
        font-size: 2.5rem;
    }

    .reseller-hero-stats {
        flex-direction: column;
        gap: 2rem;
    }

    .reseller-hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .reseller-packages-grid {
        grid-template-columns: 1fr;
    }

    .reseller-package-card.popular {
        transform: none;
    }

    .stories-grid {
        grid-template-columns: 1fr;
    }

    .story-stats {
        justify-content: center;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}
