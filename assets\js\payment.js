// Payment processing functionality for BadBoyz IPTV

// Initialize Stripe (replace with your actual publishable key)
const stripe = Stripe('pk_test_your_stripe_publishable_key_here');
const elements = stripe.elements();

// Payment method selection
document.addEventListener('DOMContentLoaded', () => {
    const paymentMethods = document.querySelectorAll('.payment-method');
    const paymentForms = document.querySelectorAll('.payment-form');
    
    paymentMethods.forEach(method => {
        method.addEventListener('click', () => {
            // Remove active class from all methods
            paymentMethods.forEach(m => m.classList.remove('active'));
            paymentForms.forEach(f => f.classList.remove('active'));
            
            // Add active class to selected method
            method.classList.add('active');
            
            // Show corresponding form
            const methodType = method.getAttribute('data-method');
            const targetForm = document.getElementById(`${methodType}Payment`);
            if (targetForm) {
                targetForm.classList.add('active');
            }
        });
    });
    
    // Initialize payment form handlers
    initializePaymentForms();
    initializeCardValidation();
});

function initializePaymentForms() {
    const paymentForm = document.getElementById('paymentForm');
    if (paymentForm) {
        paymentForm.addEventListener('submit', handlePaymentSubmission);
    }
}

function initializeCardValidation() {
    // Card number formatting
    const cardNumberInput = document.getElementById('cardNumber');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', formatCardNumber);
        cardNumberInput.addEventListener('input', detectCardType);
    }
    
    // Expiry date formatting
    const expiryInput = document.getElementById('expiryDate');
    if (expiryInput) {
        expiryInput.addEventListener('input', formatExpiryDate);
    }
    
    // CVV validation
    const cvvInput = document.getElementById('cvv');
    if (cvvInput) {
        cvvInput.addEventListener('input', formatCVV);
    }
}

function formatCardNumber(e) {
    let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
    
    if (formattedValue.length > 19) {
        formattedValue = formattedValue.substring(0, 19);
    }
    
    e.target.value = formattedValue;
}

function detectCardType(e) {
    const value = e.target.value.replace(/\s/g, '');
    let cardType = 'unknown';
    
    if (/^4/.test(value)) {
        cardType = 'visa';
    } else if (/^5[1-5]/.test(value) || /^2[2-7]/.test(value)) {
        cardType = 'mastercard';
    } else if (/^3[47]/.test(value)) {
        cardType = 'amex';
    } else if (/^6/.test(value)) {
        cardType = 'discover';
    }
    
    // Update card icon (you can add visual feedback here)
    e.target.setAttribute('data-card-type', cardType);
}

function formatExpiryDate(e) {
    let value = e.target.value.replace(/\D/g, '');
    
    if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
    }
    
    e.target.value = value;
}

function formatCVV(e) {
    let value = e.target.value.replace(/\D/g, '');
    
    if (value.length > 4) {
        value = value.substring(0, 4);
    }
    
    e.target.value = value;
}

function validateCardNumber(cardNumber) {
    const cleanNumber = cardNumber.replace(/\s/g, '');
    
    // Luhn algorithm
    let sum = 0;
    let isEven = false;
    
    for (let i = cleanNumber.length - 1; i >= 0; i--) {
        let digit = parseInt(cleanNumber.charAt(i), 10);
        
        if (isEven) {
            digit *= 2;
            if (digit > 9) {
                digit -= 9;
            }
        }
        
        sum += digit;
        isEven = !isEven;
    }
    
    return sum % 10 === 0 && cleanNumber.length >= 13;
}

function validateExpiryDate(expiryDate) {
    const [month, year] = expiryDate.split('/');
    
    if (!month || !year || month.length !== 2 || year.length !== 2) {
        return false;
    }
    
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt('20' + year, 10);
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    
    if (monthNum < 1 || monthNum > 12) {
        return false;
    }
    
    if (yearNum < currentYear || (yearNum === currentYear && monthNum < currentMonth)) {
        return false;
    }
    
    return true;
}

function validateCVV(cvv) {
    return /^\d{3,4}$/.test(cvv);
}

async function handlePaymentSubmission(e) {
    e.preventDefault();
    
    const activePaymentMethod = document.querySelector('.payment-method.active');
    const methodType = activePaymentMethod.getAttribute('data-method');
    
    const submitBtn = e.target.querySelector('.payment-btn');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="loading"></span> Processing...';
    submitBtn.disabled = true;
    
    try {
        switch (methodType) {
            case 'card':
                await processCardPayment(e.target);
                break;
            case 'paypal':
                await processPayPalPayment();
                break;
            case 'crypto':
                await processCryptoPayment();
                break;
            default:
                throw new Error('Invalid payment method');
        }
    } catch (error) {
        showPaymentError(error.message);
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

async function processCardPayment(form) {
    const formData = new FormData(form);
    
    // Validate card details
    const cardNumber = formData.get('cardNumber');
    const expiryDate = formData.get('expiryDate');
    const cvv = formData.get('cvv');
    const cardName = formData.get('cardName');
    
    if (!validateCardNumber(cardNumber)) {
        throw new Error('Invalid card number');
    }
    
    if (!validateExpiryDate(expiryDate)) {
        throw new Error('Invalid expiry date');
    }
    
    if (!validateCVV(cvv)) {
        throw new Error('Invalid CVV');
    }
    
    if (!cardName.trim()) {
        throw new Error('Cardholder name is required');
    }
    
    // Simulate payment processing
    await simulatePaymentProcessing();
    
    // Show success
    showPaymentSuccess('Card payment processed successfully!');
}

async function processPayPalPayment() {
    // Simulate PayPal redirect
    await simulatePaymentProcessing();
    showPaymentSuccess('PayPal payment completed successfully!');
}

async function processCryptoPayment() {
    const selectedCrypto = document.querySelector('.crypto-option.active');
    if (!selectedCrypto) {
        throw new Error('Please select a cryptocurrency');
    }
    
    // Simulate crypto payment
    await simulatePaymentProcessing();
    showPaymentSuccess('Cryptocurrency payment completed successfully!');
}

function simulatePaymentProcessing() {
    return new Promise((resolve) => {
        setTimeout(resolve, 3000); // Simulate 3 second processing time
    });
}

function showPaymentSuccess(message) {
    // Close payment modal
    closeModal('paymentModal');
    
    // Show success message
    const successModal = createSuccessModal(message);
    document.body.appendChild(successModal);
    
    setTimeout(() => {
        successModal.classList.add('active');
    }, 100);
    
    // Auto-close after 5 seconds
    setTimeout(() => {
        successModal.remove();
    }, 5000);
}

function showPaymentError(message) {
    const errorAlert = document.createElement('div');
    errorAlert.className = 'alert alert-error';
    errorAlert.textContent = message;
    errorAlert.style.position = 'fixed';
    errorAlert.style.top = '20px';
    errorAlert.style.right = '20px';
    errorAlert.style.zIndex = '3000';
    errorAlert.style.maxWidth = '400px';
    
    document.body.appendChild(errorAlert);
    
    setTimeout(() => {
        errorAlert.remove();
    }, 5000);
}

function createSuccessModal(message) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Payment Successful!</h2>
            </div>
            <div class="modal-body" style="text-align: center; padding: 3rem;">
                <div style="font-size: 4rem; color: var(--accent-color); margin-bottom: 1rem;">
                    <i class="fas fa-check-circle"></i>
                </div>
                <p style="font-size: 1.2rem; margin-bottom: 2rem;">${message}</p>
                <p>You will receive a confirmation email shortly with your account details.</p>
                <button class="btn btn-primary" onclick="this.closest('.modal').remove()">
                    Continue
                </button>
            </div>
        </div>
    `;
    
    return modal;
}

// Crypto option selection
document.addEventListener('DOMContentLoaded', () => {
    const cryptoOptions = document.querySelectorAll('.crypto-option');
    
    cryptoOptions.forEach(option => {
        option.addEventListener('click', () => {
            cryptoOptions.forEach(opt => opt.classList.remove('active'));
            option.classList.add('active');
        });
    });
});

// Security features
function generateTransactionId() {
    return 'TXN_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9).toUpperCase();
}

function encryptSensitiveData(data) {
    // In a real implementation, use proper encryption
    // This is just for demonstration
    return btoa(JSON.stringify(data));
}

function logPaymentAttempt(method, success, transactionId) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        method: method,
        success: success,
        transactionId: transactionId,
        userAgent: navigator.userAgent,
        ip: 'hidden' // Would be handled server-side
    };
    
    console.log('Payment attempt logged:', logEntry);
    // In production, send to secure logging service
}

// Export functions for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        validateCardNumber,
        validateExpiryDate,
        validateCVV,
        formatCardNumber,
        formatExpiryDate,
        formatCVV
    };
}
