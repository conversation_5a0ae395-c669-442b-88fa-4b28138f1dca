// Main JavaScript functionality for BadBoyz IPTV website

// Initialize AOS (Animate On Scroll)
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });
});

// Navigation functionality
const navbar = document.getElementById('navbar');
const hamburger = document.getElementById('hamburger');
const navMenu = document.getElementById('nav-menu');

// Navbar scroll effect
window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});

// Mobile menu toggle
hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
    });
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Stats counter animation
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-count'));
        const increment = target / 100;
        let current = 0;
        
        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        updateCounter();
    });
}

// Trigger counter animation when stats section is visible
const statsSection = document.querySelector('.stats');
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateCounters();
            observer.unobserve(entry.target);
        }
    });
});

if (statsSection) {
    observer.observe(statsSection);
}

// Billing toggle functionality
const billingToggle = document.getElementById('billingToggle');
if (billingToggle) {
    billingToggle.addEventListener('change', function() {
        const monthlyPrices = document.querySelectorAll('.monthly-price');
        const yearlyPrices = document.querySelectorAll('.yearly-price');
        const monthlyPeriods = document.querySelectorAll('.monthly-period');
        const yearlyPeriods = document.querySelectorAll('.yearly-period');
        
        if (this.checked) {
            // Show yearly prices
            monthlyPrices.forEach(price => price.style.display = 'none');
            yearlyPrices.forEach(price => price.style.display = 'inline');
            monthlyPeriods.forEach(period => period.style.display = 'none');
            yearlyPeriods.forEach(period => period.style.display = 'inline');
        } else {
            // Show monthly prices
            monthlyPrices.forEach(price => price.style.display = 'inline');
            yearlyPrices.forEach(price => price.style.display = 'none');
            monthlyPeriods.forEach(period => period.style.display = 'inline');
            yearlyPeriods.forEach(period => period.style.display = 'none');
        }
    });
}

// Channel data and filtering
const channelData = {
    entertainment: [
        { name: 'HBO', logo: 'fas fa-film' },
        { name: 'Netflix', logo: 'fas fa-play' },
        { name: 'Disney+', logo: 'fas fa-magic' },
        { name: 'Hulu', logo: 'fas fa-tv' },
        { name: 'Prime Video', logo: 'fas fa-video' },
        { name: 'Showtime', logo: 'fas fa-star' }
    ],
    sports: [
        { name: 'ESPN', logo: 'fas fa-football-ball' },
        { name: 'Fox Sports', logo: 'fas fa-running' },
        { name: 'NBA TV', logo: 'fas fa-basketball-ball' },
        { name: 'NFL Network', logo: 'fas fa-football-ball' },
        { name: 'MLB Network', logo: 'fas fa-baseball-ball' },
        { name: 'Tennis Channel', logo: 'fas fa-table-tennis' }
    ],
    news: [
        { name: 'CNN', logo: 'fas fa-newspaper' },
        { name: 'BBC News', logo: 'fas fa-globe' },
        { name: 'Fox News', logo: 'fas fa-broadcast-tower' },
        { name: 'MSNBC', logo: 'fas fa-microphone' },
        { name: 'Sky News', logo: 'fas fa-satellite' },
        { name: 'Al Jazeera', logo: 'fas fa-globe-americas' }
    ],
    kids: [
        { name: 'Cartoon Network', logo: 'fas fa-child' },
        { name: 'Nickelodeon', logo: 'fas fa-smile' },
        { name: 'Disney Channel', logo: 'fas fa-magic' },
        { name: 'PBS Kids', logo: 'fas fa-graduation-cap' },
        { name: 'Boomerang', logo: 'fas fa-rocket' },
        { name: 'Universal Kids', logo: 'fas fa-star' }
    ]
};

// Get all channels
function getAllChannels() {
    const allChannels = [];
    Object.values(channelData).forEach(category => {
        allChannels.push(...category);
    });
    return allChannels;
}

// Display channels
function displayChannels(channels) {
    const channelsGrid = document.getElementById('channelsGrid');
    if (!channelsGrid) return;
    
    channelsGrid.innerHTML = '';
    
    channels.forEach(channel => {
        const channelElement = document.createElement('div');
        channelElement.className = 'channel-item';
        channelElement.innerHTML = `
            <div class="channel-logo">
                <i class="${channel.logo}"></i>
            </div>
            <div class="channel-name">${channel.name}</div>
        `;
        channelsGrid.appendChild(channelElement);
    });
}

// Show channels by category
function showChannels(category) {
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    
    const activeTab = document.querySelector(`[onclick="showChannels('${category}')"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }
    
    if (category === 'all') {
        displayChannels(getAllChannels());
    } else {
        displayChannels(channelData[category] || []);
    }
}

// Initialize channels display
document.addEventListener('DOMContentLoaded', () => {
    showChannels('all');
});

// FAQ functionality
document.querySelectorAll('.faq-question').forEach(question => {
    question.addEventListener('click', () => {
        const faqItem = question.parentElement;
        const isActive = faqItem.classList.contains('active');
        
        // Close all FAQ items
        document.querySelectorAll('.faq-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Open clicked item if it wasn't active
        if (!isActive) {
            faqItem.classList.add('active');
        }
    });
});

// Modal functionality
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
    }
}

function switchModal(currentModalId, newModalId) {
    closeModal(currentModalId);
    setTimeout(() => openModal(newModalId), 300);
}

// Close modal when clicking outside
document.querySelectorAll('.modal').forEach(modal => {
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal(modal.id);
        }
    });
});

// Package selection
function selectPackage(packageType) {
    const packageNames = {
        basic: 'Basic Package',
        premium: 'Premium Package',
        ultimate: 'Ultimate Package'
    };
    
    const packagePrices = {
        basic: '$15.00',
        premium: '$25.00',
        ultimate: '$35.00'
    };
    
    // Update payment modal with selected package
    const selectedPackageName = document.getElementById('selectedPackageName');
    const selectedPackagePrice = document.getElementById('selectedPackagePrice');
    const totalPrice = document.getElementById('totalPrice');
    
    if (selectedPackageName) selectedPackageName.textContent = packageNames[packageType];
    if (selectedPackagePrice) selectedPackagePrice.textContent = packagePrices[packageType];
    if (totalPrice) totalPrice.textContent = packagePrices[packageType];
    
    openModal('paymentModal');
}

// Form submissions
document.addEventListener('DOMContentLoaded', () => {
    // Contact form
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }
    
    // Trial form
    const trialForm = document.getElementById('trialForm');
    if (trialForm) {
        trialForm.addEventListener('submit', handleTrialForm);
    }
    
    // Ticket form
    const ticketForm = document.getElementById('ticketForm');
    if (ticketForm) {
        ticketForm.addEventListener('submit', handleTicketForm);
    }
    
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLoginForm);
    }
});

function handleContactForm(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="loading"></span> Sending...';
    submitBtn.disabled = true;
    
    // Simulate form submission
    setTimeout(() => {
        showAlert('Message sent successfully! We\'ll get back to you within 2 hours.', 'success');
        e.target.reset();
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

function handleTrialForm(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="loading"></span> Setting up trial...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        showAlert('Trial activated! Check your email for login credentials.', 'success');
        closeModal('trialModal');
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        e.target.reset();
    }, 3000);
}

function handleTicketForm(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="loading"></span> Creating ticket...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        const ticketId = Math.random().toString(36).substr(2, 9).toUpperCase();
        showAlert(`Support ticket #${ticketId} created successfully!`, 'success');
        closeModal('ticketModal');
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        e.target.reset();
    }, 2000);
}

function handleLoginForm(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="loading"></span> Logging in...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        showAlert('Login successful! Redirecting to dashboard...', 'success');
        closeModal('loginModal');
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '3000';
    alert.style.maxWidth = '400px';
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        alert.remove();
    }, 5000);
}

// Make functions globally available
window.showChannels = showChannels;
window.openModal = openModal;
window.closeModal = closeModal;
window.switchModal = switchModal;
window.selectPackage = selectPackage;
