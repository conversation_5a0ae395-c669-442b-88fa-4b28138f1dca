// Customer support functionality for BadBoyz IPTV

// Live chat functionality
class LiveChat {
    constructor() {
        this.isOpen = false;
        this.messages = [];
        this.botResponses = [
            "Hello! How can I help you today?",
            "I understand your concern. Let me help you with that.",
            "Thank you for contacting us. Is there anything else I can help you with?",
            "I'll connect you with a specialist who can better assist you.",
            "Your issue has been noted. We'll get back to you shortly.",
            "For technical issues, please try restarting your device first.",
            "You can find setup guides in our help center.",
            "Our premium support team will contact you within 30 minutes."
        ];
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.addInitialMessage();
    }
    
    bindEvents() {
        const chatHeader = document.querySelector('.chat-header');
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.querySelector('.chat-input button');
        
        if (chatHeader) {
            chatHeader.addEventListener('click', () => this.toggleChat());
        }
        
        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendMessage();
                }
            });
        }
        
        if (sendButton) {
            sendButton.addEventListener('click', () => this.sendMessage());
        }
    }
    
    toggleChat() {
        const chatWidget = document.getElementById('liveChatWidget');
        this.isOpen = !this.isOpen;
        
        if (this.isOpen) {
            chatWidget.classList.add('open');
        } else {
            chatWidget.classList.remove('open');
        }
    }
    
    addInitialMessage() {
        const initialMessage = {
            type: 'bot',
            content: 'Hello! How can I help you today?',
            timestamp: new Date()
        };
        this.messages.push(initialMessage);
    }
    
    sendMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();
        
        if (!message) return;
        
        // Add user message
        this.addMessage('user', message);
        chatInput.value = '';
        
        // Simulate typing indicator
        this.showTypingIndicator();
        
        // Generate bot response after delay
        setTimeout(() => {
            this.hideTypingIndicator();
            this.generateBotResponse(message);
        }, 1500);
    }
    
    addMessage(type, content) {
        const message = {
            type: type,
            content: content,
            timestamp: new Date()
        };
        
        this.messages.push(message);
        this.renderMessage(message);
        this.scrollToBottom();
    }
    
    renderMessage(message) {
        const chatMessages = document.getElementById('chatMessages');
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${message.type}`;
        
        messageElement.innerHTML = `
            <div class="message-content">
                <p>${message.content}</p>
            </div>
        `;
        
        chatMessages.appendChild(messageElement);
    }
    
    generateBotResponse(userMessage) {
        const lowerMessage = userMessage.toLowerCase();
        let response;
        
        // Simple keyword-based responses
        if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('package')) {
            response = "Our packages start at $15/month for Basic, $25/month for Premium, and $35/month for Ultimate. All include a free 24-hour trial!";
        } else if (lowerMessage.includes('trial') || lowerMessage.includes('free')) {
            response = "Yes! We offer a free 24-hour trial with full access to all channels. No credit card required to start.";
        } else if (lowerMessage.includes('device') || lowerMessage.includes('setup') || lowerMessage.includes('install')) {
            response = "Our service works on Smart TVs, Android/iOS devices, computers, Fire Stick, and more. I can send you setup instructions for your specific device.";
        } else if (lowerMessage.includes('channel') || lowerMessage.includes('content')) {
            response = "We offer 15,000+ live channels, 50,000+ movies & shows, sports packages, and international content. What type of content are you looking for?";
        } else if (lowerMessage.includes('support') || lowerMessage.includes('help') || lowerMessage.includes('problem')) {
            response = "I'm here to help! Can you describe the specific issue you're experiencing? For technical problems, I can connect you with our technical support team.";
        } else if (lowerMessage.includes('payment') || lowerMessage.includes('billing')) {
            response = "We accept all major credit cards, PayPal, and cryptocurrency. All payments are processed securely. Do you have a specific billing question?";
        } else {
            // Random generic response
            response = this.botResponses[Math.floor(Math.random() * this.botResponses.length)];
        }
        
        this.addMessage('bot', response);
    }
    
    showTypingIndicator() {
        const chatMessages = document.getElementById('chatMessages');
        const typingElement = document.createElement('div');
        typingElement.className = 'chat-message bot typing-indicator';
        typingElement.innerHTML = `
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        chatMessages.appendChild(typingElement);
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Support ticket system
class SupportTicketSystem {
    constructor() {
        this.tickets = [];
        this.init();
    }
    
    init() {
        this.loadTickets();
    }
    
    createTicket(ticketData) {
        const ticket = {
            id: this.generateTicketId(),
            ...ticketData,
            status: 'open',
            createdAt: new Date(),
            updatedAt: new Date(),
            responses: []
        };
        
        this.tickets.push(ticket);
        this.saveTickets();
        return ticket;
    }
    
    generateTicketId() {
        return 'TKT-' + Date.now().toString(36).toUpperCase() + '-' + Math.random().toString(36).substr(2, 4).toUpperCase();
    }
    
    loadTickets() {
        const stored = localStorage.getItem('supportTickets');
        if (stored) {
            this.tickets = JSON.parse(stored);
        }
    }
    
    saveTickets() {
        localStorage.setItem('supportTickets', JSON.stringify(this.tickets));
    }
    
    getTicketById(id) {
        return this.tickets.find(ticket => ticket.id === id);
    }
    
    updateTicketStatus(id, status) {
        const ticket = this.getTicketById(id);
        if (ticket) {
            ticket.status = status;
            ticket.updatedAt = new Date();
            this.saveTickets();
        }
    }
    
    addResponse(ticketId, response) {
        const ticket = this.getTicketById(ticketId);
        if (ticket) {
            ticket.responses.push({
                content: response,
                timestamp: new Date(),
                author: 'support'
            });
            ticket.updatedAt = new Date();
            this.saveTickets();
        }
    }
}

// Knowledge base search
class KnowledgeBase {
    constructor() {
        this.articles = [
            {
                id: 1,
                title: "How to set up IPTV on Smart TV",
                category: "Setup",
                content: "Step-by-step guide to configure IPTV on your Smart TV...",
                tags: ["smart tv", "setup", "installation"]
            },
            {
                id: 2,
                title: "Troubleshooting buffering issues",
                category: "Technical",
                content: "Common solutions for buffering and streaming problems...",
                tags: ["buffering", "streaming", "troubleshooting"]
            },
            {
                id: 3,
                title: "Payment and billing information",
                category: "Billing",
                content: "Information about payments, billing cycles, and refunds...",
                tags: ["payment", "billing", "refund"]
            },
            {
                id: 4,
                title: "Channel lineup and packages",
                category: "Content",
                content: "Complete list of available channels and package details...",
                tags: ["channels", "packages", "content"]
            }
        ];
    }
    
    search(query) {
        const lowerQuery = query.toLowerCase();
        return this.articles.filter(article => 
            article.title.toLowerCase().includes(lowerQuery) ||
            article.content.toLowerCase().includes(lowerQuery) ||
            article.tags.some(tag => tag.includes(lowerQuery))
        );
    }
    
    getByCategory(category) {
        return this.articles.filter(article => article.category === category);
    }
    
    getById(id) {
        return this.articles.find(article => article.id === id);
    }
}

// FAQ system
class FAQSystem {
    constructor() {
        this.faqs = [
            {
                question: "How do I get started with BadBoyz IPTV?",
                answer: "Getting started is easy! Simply choose your preferred package, sign up for our free 24-hour trial, and you'll receive your login credentials via email.",
                category: "Getting Started"
            },
            {
                question: "What devices are compatible with your service?",
                answer: "Our service works on virtually any device including Smart TVs, Android/iOS devices, computers, tablets, Amazon Fire Stick, MAG boxes, and more.",
                category: "Compatibility"
            },
            {
                question: "Do you offer customer support?",
                answer: "Yes! We provide 24/7 customer support through live chat, email, and our support ticket system.",
                category: "Support"
            }
        ];
    }
    
    search(query) {
        const lowerQuery = query.toLowerCase();
        return this.faqs.filter(faq => 
            faq.question.toLowerCase().includes(lowerQuery) ||
            faq.answer.toLowerCase().includes(lowerQuery)
        );
    }
    
    getByCategory(category) {
        return this.faqs.filter(faq => faq.category === category);
    }
}

// Initialize support systems
document.addEventListener('DOMContentLoaded', () => {
    // Initialize live chat
    window.liveChat = new LiveChat();
    
    // Initialize support ticket system
    window.ticketSystem = new SupportTicketSystem();
    
    // Initialize knowledge base
    window.knowledgeBase = new KnowledgeBase();
    
    // Initialize FAQ system
    window.faqSystem = new FAQSystem();
    
    // Add CSS for typing indicator
    addTypingIndicatorStyles();
});

function addTypingIndicatorStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .typing-dots {
            display: flex;
            gap: 4px;
            padding: 8px 0;
        }
        
        .typing-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--text-light);
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        .typing-dots span:nth-child(3) { animation-delay: 0s; }
        
        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);
}

// Global functions for opening support features
function openLiveChat() {
    if (window.liveChat) {
        window.liveChat.toggleChat();
    }
}

function searchKnowledgeBase(query) {
    if (window.knowledgeBase) {
        return window.knowledgeBase.search(query);
    }
    return [];
}

function createSupportTicket(ticketData) {
    if (window.ticketSystem) {
        return window.ticketSystem.createTicket(ticketData);
    }
    return null;
}

// Make functions globally available
window.openLiveChat = openLiveChat;
window.searchKnowledgeBase = searchKnowledgeBase;
window.createSupportTicket = createSupportTicket;
window.sendMessage = () => {
    if (window.liveChat) {
        window.liveChat.sendMessage();
    }
};
window.toggleChat = () => {
    if (window.liveChat) {
        window.liveChat.toggleChat();
    }
};
