// Reseller page functionality for BadBoyz IPTV

// Reseller package selection
function selectResellerPackage(packageType) {
    const packageDetails = {
        starter: {
            name: 'Starter Package',
            price: '$2/credit',
            minOrder: '50 Credits',
            cost: '$100',
            profit: '$23/sale (92%)'
        },
        professional: {
            name: 'Professional Package',
            price: '$2/credit',
            minOrder: '100 Credits',
            cost: '$200',
            profit: '$23/sale (92%)'
        },
        enterprise: {
            name: 'Enterprise Package',
            price: '$1.40/credit',
            minOrder: '500 Credits',
            cost: '$700',
            profit: '$23.60/sale (94%)'
        }
    };
    
    const selectedPackage = packageDetails[packageType];
    
    // Update the reseller application modal with selected package
    const resellerPackageSelect = document.getElementById('resellerPackage');
    if (resellerPackageSelect) {
        resellerPackageSelect.value = packageType;
    }
    
    // Open the application modal
    openModal('resellerApplicationModal');
}

// Handle reseller application form submission
document.addEventListener('DOMContentLoaded', () => {
    const resellerApplicationForm = document.getElementById('resellerApplicationForm');
    if (resellerApplicationForm) {
        resellerApplicationForm.addEventListener('submit', handleResellerApplication);
    }
});

function handleResellerApplication(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const applicationData = {
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        company: formData.get('company'),
        country: formData.get('country'),
        package: formData.get('package'),
        experience: formData.get('experience'),
        goals: formData.get('goals'),
        terms: formData.get('terms')
    };
    
    // Validate required fields
    if (!applicationData.firstName || !applicationData.lastName || !applicationData.email || 
        !applicationData.phone || !applicationData.country || !applicationData.package || 
        !applicationData.terms) {
        showAlert('Please fill in all required fields and accept the terms.', 'error');
        return;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(applicationData.email)) {
        showAlert('Please enter a valid email address.', 'error');
        return;
    }
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="loading"></span> Submitting Application...';
    submitBtn.disabled = true;
    
    // Simulate application submission
    setTimeout(() => {
        // Generate application ID
        const applicationId = 'RSL-' + Date.now().toString(36).toUpperCase() + '-' + Math.random().toString(36).substr(2, 4).toUpperCase();
        
        // Store application data (in real app, this would be sent to server)
        const applications = JSON.parse(localStorage.getItem('resellerApplications') || '[]');
        applications.push({
            id: applicationId,
            ...applicationData,
            status: 'pending',
            submittedAt: new Date().toISOString()
        });
        localStorage.setItem('resellerApplications', JSON.stringify(applications));
        
        // Show success message
        showResellerApplicationSuccess(applicationId);
        
        // Reset form and close modal
        e.target.reset();
        closeModal('resellerApplicationModal');
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        
    }, 3000);
}

function showResellerApplicationSuccess(applicationId) {
    const successModal = createResellerSuccessModal(applicationId);
    document.body.appendChild(successModal);
    
    setTimeout(() => {
        successModal.classList.add('active');
    }, 100);
    
    // Auto-close after 8 seconds
    setTimeout(() => {
        successModal.remove();
    }, 8000);
}

function createResellerSuccessModal(applicationId) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Application Submitted Successfully!</h2>
            </div>
            <div class="modal-body" style="text-align: center; padding: 3rem;">
                <div style="font-size: 4rem; color: var(--secondary-color); margin-bottom: 1rem;">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Welcome to BadBoyz IPTV Reseller Program!</h3>
                <p style="font-size: 1.1rem; margin-bottom: 1.5rem;">Your application ID: <strong>${applicationId}</strong></p>
                <div style="background: var(--bg-secondary); padding: 1.5rem; border-radius: 12px; margin-bottom: 2rem;">
                    <h4 style="margin-bottom: 1rem; color: var(--text-primary);">What's Next?</h4>
                    <ul style="text-align: left; color: var(--text-secondary); line-height: 1.8;">
                        <li>✅ Application review within 24 hours</li>
                        <li>✅ Email confirmation with account details</li>
                        <li>✅ Access to reseller dashboard</li>
                        <li>✅ Marketing materials and training</li>
                    </ul>
                </div>
                <p style="margin-bottom: 2rem; color: var(--text-secondary);">
                    Our reseller team will contact you within 24 hours to complete your setup.
                </p>
                <button class="btn btn-primary" onclick="this.closest('.modal').remove()">
                    Continue
                </button>
            </div>
        </div>
    `;
    
    return modal;
}

// Reseller calculator functionality
function calculateResellerProfit() {
    const packageRates = {
        starter: { cost: 2, retail: 25 },
        professional: { cost: 2, retail: 25 },
        enterprise: { cost: 1.40, retail: 25 }
    };
    
    return packageRates;
}

// Animate profit numbers on scroll
function animateResellerStats() {
    const statNumbers = document.querySelectorAll('.reseller-hero .stat-number');
    
    statNumbers.forEach(stat => {
        const finalValue = stat.textContent;
        let currentValue = 0;
        const increment = finalValue.includes('%') ? 1 : 10;
        const isPercentage = finalValue.includes('%');
        const isPlus = finalValue.includes('+');
        const numericValue = parseInt(finalValue.replace(/[^\d]/g, ''));
        
        const updateStat = () => {
            if (currentValue < numericValue) {
                currentValue += increment;
                if (currentValue > numericValue) currentValue = numericValue;
                
                let displayValue = currentValue;
                if (isPercentage) displayValue += '%';
                if (isPlus) displayValue += '+';
                
                stat.textContent = displayValue;
                requestAnimationFrame(updateStat);
            }
        };
        
        updateStat();
    });
}

// Trigger animations when reseller hero is visible
const resellerHero = document.querySelector('.reseller-hero');
if (resellerHero) {
    const resellerObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateResellerStats();
                resellerObserver.unobserve(entry.target);
            }
        });
    });
    
    resellerObserver.observe(resellerHero);
}

// Reseller package comparison
function comparePackages() {
    const packages = [
        {
            name: 'Starter',
            cost: 2,
            minOrder: 50,
            features: ['Basic dashboard', 'Email support', 'Standard materials', '30-day validity']
        },
        {
            name: 'Professional',
            cost: 2,
            minOrder: 100,
            features: ['Advanced dashboard', 'Priority support', 'Custom branding', '60-day validity', 'API access', 'Bulk tools']
        },
        {
            name: 'Enterprise',
            cost: 1.40,
            minOrder: 500,
            features: ['White-label solution', 'Dedicated manager', 'Custom domain', '90-day validity', 'Full API', 'Custom pricing', 'Marketing support']
        }
    ];
    
    return packages;
}

// Reseller ROI calculator
function calculateROI(packageType, monthlySales) {
    const packages = {
        starter: { cost: 2, retail: 25 },
        professional: { cost: 2, retail: 25 },
        enterprise: { cost: 1.40, retail: 25 }
    };
    
    const selectedPackage = packages[packageType];
    if (!selectedPackage) return null;
    
    const profitPerSale = selectedPackage.retail - selectedPackage.cost;
    const monthlyProfit = profitPerSale * monthlySales;
    const annualProfit = monthlyProfit * 12;
    const profitMargin = (profitPerSale / selectedPackage.retail) * 100;
    
    return {
        profitPerSale,
        monthlyProfit,
        annualProfit,
        profitMargin: Math.round(profitMargin)
    };
}

// Enhanced chat responses for reseller page
function getResellerChatResponse(message) {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('reseller') || lowerMessage.includes('partner')) {
        return "Great! I'd be happy to help with our reseller program. We offer three packages: Starter ($2/credit), Professional ($2/credit), and Enterprise ($1.40/credit). Which would you like to know more about?";
    } else if (lowerMessage.includes('profit') || lowerMessage.includes('earn') || lowerMessage.includes('money')) {
        return "Our resellers earn 92-94% profit margins! For example, with our Professional package, you buy credits at $2 each and sell subscriptions at $25, earning $23 profit per sale. Top resellers make $10,000+ monthly.";
    } else if (lowerMessage.includes('starter') || lowerMessage.includes('basic')) {
        return "The Starter package is perfect for new resellers. $2/credit with 50 credit minimum order ($100). You'll earn $23 profit per $25 sale (92% margin). Includes basic dashboard and email support.";
    } else if (lowerMessage.includes('professional') || lowerMessage.includes('pro')) {
        return "Our most popular choice! Professional package: $2/credit, 100 credit minimum ($200). Earn $23 per sale (92% margin). Includes advanced dashboard, priority support, and custom branding options.";
    } else if (lowerMessage.includes('enterprise') || lowerMessage.includes('white label')) {
        return "Enterprise is for serious businesses. $1.40/credit, 500 credit minimum ($700). Earn $23.60 per sale (94% margin). Full white-label solution with dedicated account manager and custom domain.";
    } else if (lowerMessage.includes('apply') || lowerMessage.includes('application') || lowerMessage.includes('join')) {
        return "Ready to apply? Click the 'Apply Now' button to fill out our reseller application. Most applications are approved within 24 hours, and you can start selling immediately after approval!";
    } else if (lowerMessage.includes('support') || lowerMessage.includes('help')) {
        return "We provide excellent reseller support! Starter gets email support, Professional gets priority support, and Enterprise gets a dedicated account manager. Plus marketing materials and training for all levels.";
    }
    
    return "I'm here to help with our reseller program! We offer great profit margins, full support, and everything you need to build a successful IPTV business. What would you like to know?";
}

// Override the chat response function for reseller page
if (window.liveChat && typeof window.liveChat.generateBotResponse === 'function') {
    const originalGenerateBotResponse = window.liveChat.generateBotResponse;
    window.liveChat.generateBotResponse = function(userMessage) {
        const resellerResponse = getResellerChatResponse(userMessage);
        this.addMessage('bot', resellerResponse);
    };
}

// Make functions globally available
window.selectResellerPackage = selectResellerPackage;
window.calculateROI = calculateROI;
window.comparePackages = comparePackages;
